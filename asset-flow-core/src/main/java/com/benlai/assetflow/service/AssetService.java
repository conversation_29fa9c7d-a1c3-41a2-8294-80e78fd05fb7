package com.benlai.assetflow.service;

import com.benlai.assetflow.data.entity.primary.AssetFlow;
import com.benlai.assetflow.mapper.AssetFlowMapper;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Inject;
import com.benlai.assetflow.data.repository.*;
import com.benlai.assetflow.dto.*;

@ApplicationScoped
public class AssetService {

    @Inject
    AssetFlowRepository assetflowRepository;

    @Inject
    AssetFlowMapper assetFlowMapper;

    public Long save(AssetFlowDto assetFlow) {
        AssetFlow assetFlowEntity = assetFlowMapper.dtoToAssetFlow(assetFlow);
        return assetflowRepository.save(assetFlowEntity);
    }

}