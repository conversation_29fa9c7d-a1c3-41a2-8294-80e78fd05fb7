package com.benlai.assetflow.dto;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Positive;
import java.math.BigDecimal;
import java.time.LocalDateTime;

public record AssetDTO(
    @NotNull(message = "ID cannot be null")
    Long id,

    @NotBlank(message = "Name cannot be blank")
    String name,

    @NotBlank(message = "Asset type cannot be blank")
    String type,

    @NotNull(message = "Value cannot be null")
    @Positive(message = "Value must be positive")
    BigDecimal value,

    @NotNull(message = "Status cannot be null")
    String status,

    @NotNull(message = "Creation date cannot be null")
    LocalDateTime createdAt,

    String description
) {
    // Compact constructor for validation
    public AssetDTO {
        if (value != null && value.scale() > 2) {
            value = value.setScale(2, BigDecimal.ROUND_HALF_UP);
        }
    }

    // Static factory method for creating a new asset
    public static AssetDTO createNew(String name, String type, BigDecimal value, String status, String description) {
        return new AssetDTO(
            null,
            name,
            type,
            value,
            status,
            LocalDateTime.now(),
            description
        );
    }
}