package com.benlai.assetflow.data.entity.primary;

import jakarta.persistence.*;

import java.math.BigDecimal;
import java.time.Instant;
import java.time.LocalDate;

@Entity
@Table(name = "asset_stock_daily")
public class AssetStockDaily {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id", nullable = false)
    private Long id;

    @jakarta.validation.constraints.NotNull
    @Column(name = "trade_date", nullable = false)
    private LocalDate tradeDate;

    @jakarta.validation.constraints.Size(max = 20)
    @jakarta.validation.constraints.NotNull
    @Column(name = "company_no", nullable = false, length = 20)
    private String companyNo;

    @jakarta.validation.constraints.Size(max = 20)
    @jakarta.validation.constraints.NotNull
    @Column(name = "asset_type", nullable = false, length = 20)
    private String assetType;

    @jakarta.validation.constraints.Size(max = 40)
    @jakarta.validation.constraints.NotNull
    @Column(name = "asset_no", nullable = false, length = 40)
    private String assetNo;

    @jakarta.validation.constraints.NotNull
    @Column(name = "asset_qty", nullable = false, precision = 12, scale = 3)
    private BigDecimal assetQty;

    @jakarta.validation.constraints.NotNull
    @Column(name = "asset_amount", nullable = false, precision = 16, scale = 2)
    private BigDecimal assetAmount;

    @jakarta.validation.constraints.NotNull
    @Column(name = "update_time", nullable = false)
    private Instant updateTime;

    @jakarta.validation.constraints.NotNull
    @Column(name = "create_time", nullable = false)
    private Instant createTime;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public LocalDate getTradeDate() {
        return tradeDate;
    }

    public void setTradeDate(LocalDate tradeDate) {
        this.tradeDate = tradeDate;
    }

    public String getCompanyNo() {
        return companyNo;
    }

    public void setCompanyNo(String companyNo) {
        this.companyNo = companyNo;
    }

    public String getAssetType() {
        return assetType;
    }

    public void setAssetType(String assetType) {
        this.assetType = assetType;
    }

    public String getAssetNo() {
        return assetNo;
    }

    public void setAssetNo(String assetNo) {
        this.assetNo = assetNo;
    }

    public BigDecimal getAssetQty() {
        return assetQty;
    }

    public void setAssetQty(BigDecimal assetQty) {
        this.assetQty = assetQty;
    }

    public BigDecimal getAssetAmount() {
        return assetAmount;
    }

    public void setAssetAmount(BigDecimal assetAmount) {
        this.assetAmount = assetAmount;
    }

    public Instant getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Instant updateTime) {
        this.updateTime = updateTime;
    }

    public Instant getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Instant createTime) {
        this.createTime = createTime;
    }

}