quarkus:
  # --- 数据源配置 ---
  datasource:
    # 第一个数据源
    primary-datasource: # 自定义数据源名称
      db-kind: mysql
      username: db_admin
      password: 'BLSHbranch@DBA'
      jdbc:
        url: *****************************************************************
        max-size: 10 # 连接池最大连接数

    # 第二个数据源 (例如 MySQL)
    secondary-datasource: # 自定义数据源名称
      db-kind: mysql
      username: db_admin
      password: 'BLSHbranch@DBA'
      jdbc:
        url: *****************************************************************
        max-size: 10 # 连接池最大连接数

  # --- Hibernate ORM 持久化单元配置 ---
  hibernate-orm:
    # 第一个持久化单元
    primary: # 自定义持久化单元名称
      datasource: primary-datasource # 引用上面定义的数据源名称
      packages: "com.benlai.data.entity.primary" # 此PU管理的实体所在的包
      database:
        generation: update # 数据库表结构生成策略

    # 第二个持久化单元
    secondary: # 自定义持久化单元名称
      datasource: secondary-datasource # 引用上面定义的数据源名称
      packages: "com.benlai.data.entity.secondary" # 此PU管理的实体所在的包
      database:
        generation: update
    # 可以为所有持久化单元设置默认值
    # database:
    #   generation: update
    # log:
    #   sql: true