quarkus:
  application:
    name: asset-flow
  http:
    port: 8080

  datasource:
    # 第一个数据源 (例如 PostgreSQL)
    primary-datasource: # 自定义数据源名称
      db-kind: mysql
      username: db_admin
      password: 'BLSHbranch@DBA'
      jdbc:
        url: *****************************************************************
        max-size: 10 # 连接池最大连接数

    # 第二个数据源 (例如 MySQL)
    secondary-datasource: # 自定义数据源名称
      db-kind: mysql
      username: db_admin
      password: 'BLSHbranch@DBA'
      jdbc:
        url: *****************************************************************
        max-size: 10 # 连接池最大连接数

  # --- Hibernate ORM 持久化单元配置 ---
  hibernate-orm:
    # 第一个持久化单元 (连接到 pg-datasource)
    primary: # 自定义持久化单元名称
      datasource: primary-datasource # 引用上面定义的数据源名称
      packages: "com.benlai.assetflow.data.entity.primary" # 此PU管理的实体所在的包
      database:
        generation: update # 数据库表结构生成策略

    # 第二个持久化单元 (连接到 mysql-datasource)
    secondary: # 自定义持久化单元名称
      datasource: secondary-datasource # 引用上面定义的数据源名称
      packages: "com.benlai.assetflow.data.entity.secondary" # 此PU管理的实体所在的包
      database:
        generation: update
    # 可以为所有持久化单元设置默认值
    # database:
    #   generation: update
    log:
     sql: true

  # OpenAPI Configuration
  swagger-ui:
    # enable: true # true by default if quarkus-smallrye-openapi is present
    always-include: true
  smallrye-openapi:
    path: /openapi

  # Logging Configuration
  log:
    level: INFO
    category:
      "com.example":
        level: DEBUG

  # Cache Configuration
  cache:
    caffeine:
      "asset-cache":
        initial-capacity: 100
        maximum-size: 1000
        expire-after-write: 60M

  # 添加索引配置
  index-dependency:
    asset-flow-core:
      group-id: com.benlai
      artifact-id: asset-flow-core

  arc:
    transform-unproxyable-classes: true
    # 添加自动发现 MapStruct 实现
    auto-inject-fields: true
    # 确保扫描生成的 MapStruct 实现类
    remove-unused-beans: false