package com.benlai.assetflow.data.repository;

import com.benlai.assetflow.data.annotation.Primary;
import com.benlai.assetflow.data.entity.primary.AssetFlow;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Inject;
import jakarta.persistence.EntityManager;

import java.time.Instant;
import java.util.Optional;

@ApplicationScoped
public class AssetFlowRepository {


    private final EntityManager em;

    @Inject
    public AssetFlowRepository(@Primary EntityManager em) {
        this.em = em;
    }

    public Long save(AssetFlow assetFlow) {
        if(assetFlow.getBizTime() == null) {
            assetFlow.setBizTime(Instant.now());
        }
        if(assetFlow.getFlowCreateTime() == null) {
            assetFlow.setFlowCreateTime(Instant.now());
        }
        if(assetFlow.getCreateEid() == null || assetFlow.getCreateEid().isEmpty()) {
            assetFlow.setCreateEid("System");
        }
        assetFlow.setCreateTime(Instant.now());
        em.persist(assetFlow);
        return assetFlow.getId();
    }

    public Optional<AssetFlow> findById(Long id) {
        return Optional.ofNullable(em.find(AssetFlow.class, id));
    }

    public void deleteById(Long id) {
        findById(id).ifPresent(em::remove);
    }
}
