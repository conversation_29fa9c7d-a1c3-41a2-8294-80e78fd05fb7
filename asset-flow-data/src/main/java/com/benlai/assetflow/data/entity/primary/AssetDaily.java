package com.benlai.assetflow.data.entity.primary;

import jakarta.persistence.*;

import java.math.BigDecimal;
import java.time.*;

@Entity
@Table(name = "asset_daily")
public class AssetDaily {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id", nullable = false)
    private Long id;

    @jakarta.validation.constraints.NotNull
    @Column(name = "movement_date", nullable = false)
    private LocalDate movementDate;

    @jakarta.validation.constraints.Size(max = 20)
    @jakarta.validation.constraints.NotNull
    @Column(name = "company_no", nullable = false, length = 20)
    private String companyNo;

    @jakarta.validation.constraints.Size(max = 20)
    @jakarta.validation.constraints.NotNull
    @Column(name = "asset_type", nullable = false, length = 20)
    private String assetType;

    @jakarta.validation.constraints.Size(max = 40)
    @jakarta.validation.constraints.NotNull
    @Column(name = "asset_no", nullable = false, length = 40)
    private String assetNo;

    @jakarta.validation.constraints.Size(max = 10)
    @jakarta.validation.constraints.NotNull
    @Column(name = "daily_type_code", nullable = false, length = 10)
    private String dailyTypeCode;

    @jakarta.validation.constraints.Size(max = 30)
    @jakarta.validation.constraints.NotNull
    @Column(name = "daily_type_name", nullable = false, length = 30)
    private String dailyTypeName;

    @jakarta.validation.constraints.NotNull
    @Column(name = "qty1", nullable = false, precision = 12, scale = 3)
    private BigDecimal qty1;

    @jakarta.validation.constraints.NotNull
    @Column(name = "amount1", nullable = false, precision = 16, scale = 2)
    private BigDecimal amount1;

    @jakarta.validation.constraints.NotNull
    @Column(name = "qty2", nullable = false, precision = 12, scale = 3)
    private BigDecimal qty2;

    @jakarta.validation.constraints.NotNull
    @Column(name = "amount2", nullable = false, precision = 16, scale = 2)
    private BigDecimal amount2;

    @jakarta.validation.constraints.NotNull
    @Column(name = "create_time", nullable = false)
    private Instant createTime;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public LocalDate getMovementDate() {
        return movementDate;
    }

    public void setMovementDate(LocalDate movementDate) {
        this.movementDate = movementDate;
    }

    public String getCompanyNo() {
        return companyNo;
    }

    public void setCompanyNo(String companyNo) {
        this.companyNo = companyNo;
    }

    public String getAssetType() {
        return assetType;
    }

    public void setAssetType(String assetType) {
        this.assetType = assetType;
    }

    public String getAssetNo() {
        return assetNo;
    }

    public void setAssetNo(String assetNo) {
        this.assetNo = assetNo;
    }

    public String getDailyTypeCode() {
        return dailyTypeCode;
    }

    public void setDailyTypeCode(String dailyTypeCode) {
        this.dailyTypeCode = dailyTypeCode;
    }

    public String getDailyTypeName() {
        return dailyTypeName;
    }

    public void setDailyTypeName(String dailyTypeName) {
        this.dailyTypeName = dailyTypeName;
    }

    public BigDecimal getQty1() {
        return qty1;
    }

    public void setQty1(BigDecimal qty1) {
        this.qty1 = qty1;
    }

    public BigDecimal getAmount1() {
        return amount1;
    }

    public void setAmount1(BigDecimal amount1) {
        this.amount1 = amount1;
    }

    public BigDecimal getQty2() {
        return qty2;
    }

    public void setQty2(BigDecimal qty2) {
        this.qty2 = qty2;
    }

    public BigDecimal getAmount2() {
        return amount2;
    }

    public void setAmount2(BigDecimal amount2) {
        this.amount2 = amount2;
    }

    public Instant getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Instant createTime) {
        this.createTime = createTime;
    }

}