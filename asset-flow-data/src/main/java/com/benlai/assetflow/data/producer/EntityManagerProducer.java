package com.benlai.assetflow.data.producer;


import com.benlai.assetflow.data.annotation.Primary;
import com.benlai.assetflow.data.annotation.Secondary;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.enterprise.inject.Produces;
import jakarta.inject.Inject;
import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext;

@ApplicationScoped
public class EntityManagerProducer {


    @PersistenceContext(unitName = "primary")
    EntityManager primaryEntityManager;

    @PersistenceContext(unitName = "secondary")
    EntityManager secondaryEntityManager;

    @Produces
    @Primary
    public EntityManager primaryEntityManager() {
        return primaryEntityManager;
    }

    @Produces
    @Secondary
    public EntityManager secondaryEntityManager() {
        return secondaryEntityManager;
    }
}
