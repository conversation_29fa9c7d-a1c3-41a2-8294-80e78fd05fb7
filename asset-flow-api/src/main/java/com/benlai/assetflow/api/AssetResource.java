package com.benlai.assetflow.api;

import com.benlai.assetflow.dto.AssetFlowDto;
import com.benlai.assetflow.service.AssetService;
import io.smallrye.common.annotation.NonBlocking;
import jakarta.inject.Inject;
import jakarta.validation.Valid;
import jakarta.ws.rs.*;
import jakarta.ws.rs.core.MediaType;
import jakarta.ws.rs.core.Response;
import jakarta.ws.rs.core.Response.Status;
import org.eclipse.microprofile.openapi.annotations.Operation;
import org.eclipse.microprofile.openapi.annotations.tags.Tag;

import java.util.List;

@Path("/api/assets")
@Produces(MediaType.APPLICATION_JSON)
@Consumes(MediaType.APPLICATION_JSON)
@Tag(name = "Asset Management", description = "Operations for managing assets")
public class AssetResource {


    private final AssetService assetService;

    @Inject
    public AssetResource(AssetService assetService) {
        this.assetService = assetService;
    }


    @POST
    @Operation(summary = "Get all assets", description = "Retrieve a list of all assets")
    public Response createAssetFlow(@Valid AssetFlowDto assetFlowDto) {
        Long assets = assetService.save(assetFlowDto);
        return Response.ok(assets).build();
    }



}