package com.benlai.assetflow.mapper;


import com.benlai.assetflow.data.entity.primary.AssetFlow;
import com.benlai.assetflow.dto.AssetFlowDto;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

// 使用默认组件模型
@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface AssetFlowMapper {

    AssetFlowMapper INSTANCE = Mappers.getMapper(AssetFlowMapper.class);


    @Mapping(source = "createEid", target = "createEid", defaultValue = "System")
    AssetFlow dtoToAssetFlow(AssetFlowDto assetFlowDto);
}
