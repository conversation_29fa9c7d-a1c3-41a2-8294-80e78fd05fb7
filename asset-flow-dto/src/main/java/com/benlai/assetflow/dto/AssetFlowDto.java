package com.benlai.assetflow.dto;

import com.fasterxml.jackson.annotation.JsonProperty;

import java.math.BigDecimal;
import java.time.Instant;

public record AssetFlowDto(

        @JsonProperty("flow_no")
        String flowNo,

        @JsonProperty("company_no")
        String companyNo,

        @JsonProperty("asset_type")
        String assetType,

        @JsonProperty("asset_no")
        String assetNo,

        @JsonProperty("third_biz_type_code")
        String thirdBizTypeCode,

        @JsonProperty("biz_type_code")
        String bizTypeCode,

        @JsonProperty("paper_no")
        String paperNo,

        @JsonProperty("qty")
        BigDecimal qty,
        @JsonProperty("amount")
        BigDecimal amount,
        @JsonProperty("biz_time")
        Instant bizTime,
        @JsonProperty("flow_create_time")
        Instant flowCreateTime,

        @JsonProperty("create_eid")
        String createEid
) {
}
